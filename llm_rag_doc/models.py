import os
import uuid
from django.db import models
from django.utils import timezone
from django.conf import settings
from django.utils.translation import gettext_lazy
from customer.models import Customer
from devproject.utils.azure_storage import AzureBlobStorage

class ProductProvider(models.Model):
    thai_name = models.CharField(max_length=100, unique=True)
    english_name = models.CharField(max_length=100, unique=True, blank=True, null=True)
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_provider_created_by',
        blank=True,
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_provider_updated_by',
        blank=True,
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        id = self.pk
        return f"id: {id}, name: {self.thai_name} ({self.english_name}), is_default: {self.is_default}"

class Company(models.Model):
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    color = models.CharField(max_length=100, default="grey") # Default color
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='company_created_by',
        blank=True,
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='company_updated_by',
        blank=True,
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        id = self.pk
        name = self.name
        return f"id: {id}, name: {name}"


# This Document class version work when sve file into Azure Blob storage
class Document(models.Model):
    class Topic(models.TextChoices):
        TEMPLATE = "TEMPLATE", "TEMPLATE"
        FAQ = "FAQ", "FAQ"
        RECOMMENDATION = "RECOMMENDATION", "RECOMMENDATION"
   
    class Category(models.TextChoices):
        TEMPLATE = "TEMPLATE", "Template"
        PROMOTION = "PROMOTION", "Promotion"
        CUSTOMER_SUPPORT = "CUSTOMER_SUPPORT", "Customer Support"
        PRODUCT = "PRODUCT", "Product"
        OTHER = "OTHER", "Other"

    # File information
    filename = models.CharField(max_length=255)
    filepath = models.CharField(max_length=500)  # Store the complete Azure blob path
    file_type = models.CharField(max_length=500, blank=True, null=True)  # e.g. 'pdf', 'docx', 'txt'
    
    # Document classification
    topic = models.CharField(max_length=50, choices=Topic.choices)
    category = models.CharField(max_length=50, choices=Category.choices)
    description = models.TextField(blank=True, null=True)
    companies = models.ManyToManyField(
        to='Company',
        related_name='documents_companies',
        blank=True
    )
    
    # Access control
    access_level = models.JSONField(default=list, blank=True)  # e.g. ['customer', 'agent', 'supervisor', 'admin']
    
    # Document validity period
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)

    # Status and audit fields
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='document_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='document_updated_by',
        blank=True,
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    @property
    def blob_folder(self):
        """Returns the document's blob storage folder path"""
        return f"document/{self.topic.lower()}/{self.category.lower()}/"

    def upload_file(self, file, filename=None):
        """
        Upload a file to the document's folder in Azure Blob Storage
        
        Args:
            file: File object (e.g., from request.FILES)
            filename: Optional custom filename, defaults to original filename
            
        Returns:
            URL of the uploaded blob
        """
        azure_storage = AzureBlobStorage()
        if filename is None:
            filename = file.name
        
        # Update model fields
        self.filename = filename
        blob_name = f"{self.blob_folder}{filename}"
        self.filepath = blob_name
        
        # Auto-detect file type from file extension
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
        self.file_type = file_extension
        
        # Upload file and save model
        url = azure_storage.upload_file(file, blob_name)
        self.save()
        
        return url

    def delete_file(self):
        """
        Delete the file from Azure Blob Storage and clear file fields
        
        Returns:
            Boolean indicating success
        """
        if not self.filepath:
            return False
            
        try:
            azure_storage = AzureBlobStorage()
            success = azure_storage.delete_file(self.filepath)
            
            if success:
                self.filename = None
                self.filepath = None
                self.save()
            
            return success
        except Exception:
            return False

    # def delete(self, user=None):
    #     """Soft delete the document and optionally remove the file"""
    #     # Attempt to delete file first
    #     if self.filepath:
    #         self.delete_file()
        
    #     # Soft delete
    #     self.is_active = False
    #     self.save()

    def soft_delete(self):
        """Soft delete the document and optionally remove the file"""
        self.is_active = False
        self.save()

    def __str__(self):
        return f"Document {self.id}: {self.filename} ({self.topic} - {self.category})"

    # class Meta:
    #     ordering = ['-created_on']  # Most recent first

class ProductType(models.Model):
    code = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    subtypes = models.JSONField(blank=True, null=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_type_updated_by',
        blank=True,
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"id : {self.pk}, code : {self.code}, name : {self.name} subtypes : {self.subtypes}" 
    
class Product(models.Model):

    # class ProductType(models.TextChoices):
    #     CAR = "CAR", gettext_lazy("Car")
    #     COMPULSORY_MOTOR = "COMPULSORY_MOTOR", gettext_lazy("Compulsory Motor")
    #     HTA = "HEALTH_ACCIDENT_TRAVEL", gettext_lazy("Health Accident Travel")
    #     HOME = "HOME", gettext_lazy("Home")
    #     SHIPPING = "SHIPPING", gettext_lazy("Shipping")
    #     CANCER = "CANCER", gettext_lazy("Cancer")
    #     BUSINESS = "BUSINESS", gettext_lazy("Business")
    #     CYBER = "CYBER", gettext_lazy("Cyber")

    name = models.CharField(max_length=200, blank=True, null=True)
    keyword = models.CharField(max_length=1000, blank=True, null=True)
    description = models.CharField(max_length=5000, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    product_provider = models.ForeignKey(
        to=ProductProvider,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='products'
    )

    # product_type = models.CharField(
    #     max_length=50,
    #     choices=ProductType,
    # )
    # Use ForeignKey to ProductType model
    
    product_type = models.ForeignKey(
        ProductType,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='products'
    )
    
    plan_id = models.CharField(blank=True, null=True)
    type = models.CharField(blank=True, null=True)
    coverage = models.FloatField(blank=True, null=True)
    premium = models.FloatField(blank=True, null=True)
    net_premium = models.FloatField(blank=True, null=True)
    conditions = models.TextField(blank=True, null=True)

    duration = models.IntegerField(null=False, default=0) # Number of days before policy is expired
    waiting_period = models.IntegerField(null=False, default=0) # Number of days before policy is started
    document_id = models.ForeignKey(
        to=Document,
        on_delete=models.SET_NULL,
        related_name='product_document_file',
        blank=True,
        null=True
    ) 
    image_id = models.ForeignKey(
        to=Document,
        on_delete=models.SET_NULL,
        related_name='product_document_image',
        blank=True,
        null=True
    ) 

    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)
    expired_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='product_expired_by',
        blank=True, 
        null=True
        )
    expired_on = models.DateTimeField(blank=True, null=True)

    def delete(self, user=None):
        # Soft Delete
        self.is_active = False
        self.expired_on = timezone.now()
        if user:
            self.expired_by = user
        self.save()

    def __str__(self):
        id = self.id
        name = self.name
        product_type = self.product_type
        plan_id = self.plan_id
        string = f"id:{id}, name:{name}, product_type:{product_type}, plan_id:{plan_id}"
        return string
    
class PolicyHolder(models.Model):

    class PolicyStatus(models.TextChoices):
        WAITING_PERIOD = "WAITING PERIOD", gettext_lazy("Waiting period")
        ACTIVE = "ACTIVE", gettext_lazy("Active")
        NEARLY_EXPIRED = "NEARLY EXPIRED", gettext_lazy("Nearly expired")
        EXPIRED = "EXPIRED", gettext_lazy("Expired")
        DELETED = "DELETED", gettext_lazy("Deleted")

    customer_id = models.ForeignKey(to=Customer, on_delete=models.CASCADE)
    product_id = models.ForeignKey(to=Product, on_delete=models.CASCADE)
    policy_status = models.CharField(
        max_length=50,
        choices=PolicyStatus,
    )
    issue_date = models.DateTimeField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()

    # def delete(self, user=None):
    #     self.policy_status = "DELETED"
    #     self.expired_on = timezone.now()
    #     self.save()

    def __str__(self):
        id = self.id
        customer_id = self.customer_id
        product_id = self.product_id
        policy_status = self.policy_status
        string = f"{id}, {customer_id}, {product_id}, {policy_status}"
        return string
    
from pgvector.django import VectorField

class LangchainPgCollection(models.Model):
    uuid = models.UUIDField(primary_key=True)
    name = models.CharField(blank=True, null=True)
    cmetadata = models.TextField(blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = "langchain_pg_collection"

class LangchainPgEmbedding(models .Model):
    id = models.UUIDField(primary_key=True)
    collection_id = models.ForeignKey(LangchainPgCollection, models.DO_NOTHING, blank=True, null=True)
    embedding = VectorField(dimensions=3072) # This field type is a guess.
    document = models.CharField(blank=True, null=True)
    cmetadata = models.TextField(blank=True, null=True) # This field type is a guess.

    class Meta:
        managed = False
        db_table = "langchain_pg_embedding"
        verbose_name = "Langchain Embedding"
        verbose_name_plural = "Langchain Embeddings"
    
    def __str__(self):
        """String representation for admin display"""
        return f"Embedding {str(self.id)[:8]}... - {self.document[:50] if self.document else 'No document'}"

class VectorDBRouter:
    """
    A router to control all database operations on models using vector_db
    """
    
    def db_for_read(self, model, **hints):
        if model.__name__ == 'LangchainPgEmbedding':
            return 'vector_db'
        return None
    
    def db_for_write(self, model, **hints):
        if model.__name__ == 'LangchainPgEmbedding':
            return 'vector_db'
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        if model_name == 'langchainpgembedding':
            return db == 'vector_db'
        return None